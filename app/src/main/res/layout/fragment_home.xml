<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.home.HomeFragment">

    <!-- Invisible helper view to center the group -->
    <View
        android:id="@+id/centerHelper"
        android:layout_width="1dp"
        android:layout_height="1dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="350dp"
        android:maxHeight="400dp"
        android:contentDescription="@string/nav_header_desc"
        android:scaleType="fitCenter"
        android:adjustViewBounds="true"
        android:background="@android:color/transparent"
        android:src="@drawable/ic_women"
        app:layout_constraintBottom_toTopOf="@id/centerHelper"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="8dp" />

    <TextView
        android:id="@+id/text_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textColor="@color/black"
        app:layout_constraintTop_toBottomOf="@id/centerHelper"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>