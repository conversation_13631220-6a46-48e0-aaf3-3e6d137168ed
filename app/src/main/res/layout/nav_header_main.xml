<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="@drawable/yelow_bg_gradient"
    android:paddingLeft="24dp"
    android:paddingTop="16dp"
    android:paddingRight="16dp"
    android:paddingBottom="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <!-- Spacer to center content vertically -->
    <View
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="200dp"
        android:layout_height="120dp"
        android:layout_gravity="start"
        android:contentDescription="@string/nav_header_desc"
        android:scaleType="fitStart"
        android:adjustViewBounds="true"
        android:background="@android:color/transparent"
        android:src="@drawable/ic_radioactive" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:layout_marginTop="8dp"
        android:text="@string/nav_header_subtitle"
        android:textColor="@color/black"
        android:textSize="14sp"/>

    <!-- Spacer to center content vertically -->
    <View
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_weight="1" />

</LinearLayout>