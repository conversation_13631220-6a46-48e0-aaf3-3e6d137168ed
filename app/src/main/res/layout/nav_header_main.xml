<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="@drawable/yelow_bg_gradient"
    android:gravity="bottom"
    android:orientation="vertical"
    android:paddingLeft="24dp"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="120dp"
        android:layout_height="80dp"
        android:layout_gravity="start"
        android:contentDescription="@string/nav_header_desc"
        android:paddingTop="@dimen/nav_header_vertical_spacing"
        android:scaleType="fitStart"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_radioactive" />

    <TextView
        android:id="@+id/textView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:paddingTop="8dp"
        android:text="@string/nav_header_subtitle"
        android:textColor="@color/black"
        android:textSize="14sp"/>
</LinearLayout>